#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR API 测试脚本
"""

import requests
import json

def test_health():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_ocr_with_url():
    """测试使用URL的OCR接口"""
    print("\n测试OCR接口（URL输入）...")
    try:
        url = "http://localhost:8000/ocr"
        data = {
            "input": "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"
        }
        
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("success"):
            print(f"识别成功！识别到 {len(result.get('data', []))} 个文本区域")
            for i, item in enumerate(result.get('data', [])):
                print(f"  文本{i+1}: {item.get('text')} (置信度: {item.get('confidence'):.2f})")
        
        return response.status_code == 200
    except Exception as e:
        print(f"OCR测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试PaddleOCR API...")
    
    # 测试健康检查
    health_ok = test_health()
    if not health_ok:
        print("健康检查失败，请确保服务正在运行")
        return
    
    # 测试OCR功能
    ocr_ok = test_ocr_with_url()
    if ocr_ok:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ OCR测试失败")

if __name__ == "__main__":
    main()
