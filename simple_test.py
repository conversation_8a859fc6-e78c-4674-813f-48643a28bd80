#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本，使用内置库
"""

import urllib.request
import urllib.parse
import json

def test_ocr():
    """测试OCR接口"""
    url = "http://localhost:8000/ocr"
    data = {
        "input": "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"
    }
    
    # 将数据转换为JSON
    json_data = json.dumps(data).encode('utf-8')
    
    # 创建请求
    req = urllib.request.Request(
        url,
        data=json_data,
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        # 发送请求
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
            print("Status:", response.status)
            print("Response:", json.dumps(result, ensure_ascii=False, indent=2))
            return result
    except Exception as e:
        print(f"请求失败: {e}")
        return None

if __name__ == "__main__":
    print("测试OCR API...")
    test_ocr()
