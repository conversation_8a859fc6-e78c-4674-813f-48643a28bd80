#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR API 服务
提供基于PaddleOCR的文字识别接口
"""

import os
import json
import base64
import logging
import tempfile
from io import BytesIO
from flask import Flask, request, jsonify
from flask_cors import CORS
from paddleocr import PaddleOCR
import requests
from PIL import Image
import numpy as np
import cv2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量存储OCR实例
ocr_instance = None

def init_ocr():
    """初始化PaddleOCR实例"""
    global ocr_instance
    try:
        logger.info("正在初始化PaddleOCR实例...")
        ocr_instance = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False,
            lang='ch'  # 支持中文
        )
        logger.info("PaddleOCR实例初始化成功")
    except Exception as e:
        logger.error(f"PaddleOCR初始化失败: {str(e)}")
        raise e

def download_image_from_url(url):
    """从URL下载图片并转换为numpy数组"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        # 将图片数据转换为numpy数组
        image_data = np.frombuffer(response.content, np.uint8)
        image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)

        if image is None:
            raise Exception("无法解码图片数据")

        return image
    except Exception as e:
        logger.error(f"下载图片失败: {str(e)}")
        raise Exception(f"无法下载图片: {str(e)}")

def decode_base64_image(base64_str):
    """解码base64图片并转换为numpy数组"""
    try:
        # 移除data:image前缀（如果存在）
        if base64_str.startswith('data:image'):
            base64_str = base64_str.split(',')[1]

        image_data = base64.b64decode(base64_str)

        # 将图片数据转换为numpy数组
        image_array = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

        if image is None:
            raise Exception("无法解码Base64图片数据")

        return image
    except Exception as e:
        logger.error(f"Base64解码失败: {str(e)}")
        raise Exception(f"Base64图片解码失败: {str(e)}")

def process_ocr_result(result):
    """处理OCR结果，转换为标准JSON格式"""
    processed_results = []

    try:
        logger.info(f"原始OCR结果类型: {type(result)}, 长度: {len(result) if result else 0}")

        for i, line in enumerate(result):
            logger.info(f"处理第{i+1}行: {line}")

            # PaddleOCR返回格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
            if len(line) >= 2:
                bbox = line[0]  # 边界框坐标
                text_info = line[1]  # 文本和置信度

                # 解析文本和置信度
                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                    text = text_info[0]
                    confidence = text_info[1]
                elif isinstance(text_info, str):
                    text = text_info
                    confidence = 1.0
                else:
                    logger.warning(f"未知的text_info格式: {text_info}")
                    text = str(text_info)
                    confidence = 1.0

                processed_results.append({
                    "text": text,
                    "confidence": float(confidence),
                    "bbox": bbox
                })
            else:
                logger.warning(f"跳过格式不正确的行: {line}")

    except Exception as e:
        logger.error(f"处理OCR结果失败: {str(e)}")
        logger.error(f"原始结果: {result}")
        raise Exception(f"处理OCR结果失败: {str(e)}")

    return processed_results

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "service": "PaddleOCR API",
        "ocr_ready": ocr_instance is not None
    })

@app.route('/ocr', methods=['POST'])
def ocr_recognize():
    """OCR识别接口"""
    try:
        # 检查OCR实例是否已初始化
        if ocr_instance is None:
            return jsonify({
                "success": False,
                "message": "OCR服务未初始化",
                "data": None
            }), 500

        # 获取请求数据
        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({
                "success": False,
                "message": "请求参数错误，需要提供input字段",
                "data": None
            }), 400

        input_data = data['input']
        logger.info(f"收到OCR请求，输入类型: {type(input_data)}")

        # 处理不同类型的输入
        image_source = None

        if isinstance(input_data, str):
            if input_data.startswith('http://') or input_data.startswith('https://'):
                # URL输入
                logger.info("处理URL输入")
                image_source = download_image_from_url(input_data)
            elif input_data.startswith('data:image') or len(input_data) > 100:
                # Base64输入
                logger.info("处理Base64输入")
                image_source = decode_base64_image(input_data)
            else:
                # 文件路径输入
                logger.info("处理文件路径输入")
                if os.path.exists(input_data):
                    image_source = input_data
                else:
                    return jsonify({
                        "success": False,
                        "message": f"文件不存在: {input_data}",
                        "data": None
                    }), 400
        else:
            return jsonify({
                "success": False,
                "message": "不支持的输入格式",
                "data": None
            }), 400

        # 执行OCR识别
        logger.info("开始执行OCR识别...")
        result = ocr_instance.ocr(image_source)

        # 处理结果
        if result and len(result) > 0 and result[0]:
            processed_result = process_ocr_result(result[0])
            logger.info(f"OCR识别成功，识别到 {len(processed_result)} 个文本区域")

            return jsonify({
                "success": True,
                "message": "OCR识别成功",
                "data": processed_result
            })
        else:
            logger.info("OCR识别完成，但未识别到文字")
            return jsonify({
                "success": True,
                "message": "未识别到文字内容",
                "data": []
            })

    except Exception as e:
        logger.error(f"OCR识别过程中发生错误: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"OCR识别失败: {str(e)}",
            "data": None
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        "success": False,
        "message": "接口不存在",
        "data": None
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        "success": False,
        "message": "服务器内部错误",
        "data": None
    }), 500

if __name__ == '__main__':
    try:
        # 初始化OCR
        init_ocr()

        # 启动Flask应用
        logger.info("启动PaddleOCR API服务...")
        app.run(
            host='0.0.0.0',
            port=8000,
            debug=False,
            threaded=True
        )
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        exit(1)
