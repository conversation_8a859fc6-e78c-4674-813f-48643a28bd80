#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR API 服务
提供基于PaddleOCR的文字识别接口
"""

import os
import json
import base64
import logging
import tempfile
from io import BytesIO
from flask import Flask, request, jsonify
from flask_cors import CORS
from paddleocr import PaddleOCR
import requests
from PIL import Image
import numpy as np
import cv2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量存储OCR实例
ocr_instance = None

def init_ocr():
    """初始化PaddleOCR实例"""
    global ocr_instance
    try:
        logger.info("正在初始化PaddleOCR实例...")
        ocr_instance = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False,
            lang='ch'  # 支持中文
        )
        logger.info("PaddleOCR实例初始化成功")
    except Exception as e:
        logger.error(f"PaddleOCR初始化失败: {str(e)}")
        raise e

def download_image_from_url(url):
    """从URL下载图片并转换为numpy数组"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        # 将图片数据转换为numpy数组
        image_data = np.frombuffer(response.content, np.uint8)
        image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)

        if image is None:
            raise Exception("无法解码图片数据")

        return image
    except Exception as e:
        logger.error(f"下载图片失败: {str(e)}")
        raise Exception(f"无法下载图片: {str(e)}")

def decode_base64_image(base64_str):
    """解码base64图片并转换为numpy数组"""
    try:
        # 移除data:image前缀（如果存在）
        if base64_str.startswith('data:image'):
            base64_str = base64_str.split(',')[1]

        image_data = base64.b64decode(base64_str)

        # 将图片数据转换为numpy数组
        image_array = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

        if image is None:
            raise Exception("无法解码Base64图片数据")

        return image
    except Exception as e:
        logger.error(f"Base64解码失败: {str(e)}")
        raise Exception(f"Base64图片解码失败: {str(e)}")

def process_ocr_result(result):
    """处理OCR结果，转换为标准JSON格式"""
    processed_results = []

    try:
        logger.info(f"原始OCR结果类型: {type(result)}")
        logger.info(f"原始OCR结果长度: {len(result) if result else 0}")
        logger.info(f"原始OCR结果内容: {result}")

        # 检查result是否为None或空
        if not result:
            logger.warning("OCR结果为空")
            return processed_results

        # 遍历每一行识别结果
        for i, line in enumerate(result):
            logger.info(f"处理第{i+1}行，类型: {type(line)}, 内容: {line}")

            # 检查line的格式
            if not isinstance(line, (list, tuple)) or len(line) < 2:
                logger.warning(f"跳过格式不正确的行: {line}")
                continue

            # PaddleOCR返回格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
            bbox = line[0]  # 边界框坐标
            text_info = line[1]  # 文本和置信度

            logger.info(f"bbox类型: {type(bbox)}, 内容: {bbox}")
            logger.info(f"text_info类型: {type(text_info)}, 内容: {text_info}")

            # 解析文本和置信度
            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                text = text_info[0]
                confidence = text_info[1]
            elif isinstance(text_info, str):
                text = text_info
                confidence = 1.0
            else:
                logger.warning(f"未知的text_info格式: {text_info}")
                text = str(text_info)
                confidence = 1.0

            # 验证bbox格式
            if not isinstance(bbox, (list, tuple)) or len(bbox) != 4:
                logger.warning(f"bbox格式不正确: {bbox}")
                # 如果bbox格式不正确，设置为空数组
                bbox = []

            processed_results.append({
                "text": text,
                "confidence": float(confidence),
                "bbox": bbox
            })

    except Exception as e:
        logger.error(f"处理OCR结果失败: {str(e)}")
        logger.error(f"原始结果详细信息: {result}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise Exception(f"处理OCR结果失败: {str(e)}")

    logger.info(f"处理完成，共识别到 {len(processed_results)} 个文本区域")
    return processed_results

def process_predict_result(result):
    """处理predict方法返回的字典格式结果"""
    processed_results = []

    try:
        logger.info(f"处理predict结果，类型: {type(result)}")

        if not isinstance(result, dict):
            logger.error("predict结果不是字典格式")
            return processed_results

        # 查找文本和坐标信息
        texts = result.get('rec_texts', [])
        scores = result.get('rec_scores', [])
        polys = result.get('rec_polys', [])

        logger.info(f"找到文本数量: {len(texts)}")
        logger.info(f"找到分数数量: {len(scores)}")
        logger.info(f"找到坐标数量: {len(polys)}")

        # 确保所有数组长度一致
        min_length = min(len(texts), len(scores), len(polys))
        logger.info(f"使用最小长度: {min_length}")

        for i in range(min_length):
            text = texts[i] if i < len(texts) else ""
            confidence = scores[i] if i < len(scores) else 1.0
            bbox = polys[i] if i < len(polys) else []

            logger.info(f"处理第{i+1}个结果: text='{text}', confidence={confidence}, bbox={bbox}")

            processed_results.append({
                "text": str(text),
                "confidence": float(confidence),
                "bbox": bbox
            })

    except Exception as e:
        logger.error(f"处理predict结果失败: {str(e)}")
        logger.error(f"原始结果: {result}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise Exception(f"处理predict结果失败: {str(e)}")

    logger.info(f"predict结果处理完成，共识别到 {len(processed_results)} 个文本区域")
    return processed_results

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "service": "PaddleOCR API",
        "ocr_ready": ocr_instance is not None
    })

@app.route('/ocr', methods=['POST'])
def ocr_recognize():
    """OCR识别接口"""
    try:
        # 检查OCR实例是否已初始化
        if ocr_instance is None:
            return jsonify({
                "success": False,
                "message": "OCR服务未初始化",
                "data": None
            }), 500

        # 获取请求数据
        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({
                "success": False,
                "message": "请求参数错误，需要提供input字段",
                "data": None
            }), 400

        input_data = data['input']
        logger.info(f"收到OCR请求，输入类型: {type(input_data)}")

        # 处理不同类型的输入
        image_source = None

        if isinstance(input_data, str):
            if input_data.startswith('http://') or input_data.startswith('https://'):
                # URL输入
                logger.info("处理URL输入")
                image_source = download_image_from_url(input_data)
            elif input_data.startswith('data:image') or len(input_data) > 100:
                # Base64输入
                logger.info("处理Base64输入")
                image_source = decode_base64_image(input_data)
            else:
                # 文件路径输入
                logger.info("处理文件路径输入")
                if os.path.exists(input_data):
                    image_source = input_data
                else:
                    return jsonify({
                        "success": False,
                        "message": f"文件不存在: {input_data}",
                        "data": None
                    }), 400
        else:
            return jsonify({
                "success": False,
                "message": "不支持的输入格式",
                "data": None
            }), 400

        # 执行OCR识别
        logger.info("开始执行OCR识别...")
        # 使用predict方法替代已弃用的ocr方法
        result = ocr_instance.predict(image_source)

        # 详细记录OCR原始结果
        logger.info(f"OCR原始结果类型: {type(result)}")
        logger.info(f"OCR原始结果长度: {len(result) if result else 0}")
        logger.info(f"OCR原始结果完整内容: {result}")

        # 处理结果
        if result:
            # 检查result的类型和结构
            if isinstance(result, dict):
                # 如果result是字典，查找包含OCR结果的键
                logger.info("结果是字典格式，查找OCR数据...")
                ocr_data = None

                # 常见的OCR结果键名
                possible_keys = ['rec_texts', 'texts', 'results', 'data', 'ocr_results']
                for key in possible_keys:
                    if key in result:
                        ocr_data = result[key]
                        logger.info(f"找到OCR数据，键名: {key}")
                        break

                if ocr_data is None:
                    # 如果没有找到预期的键，尝试使用整个字典
                    logger.warning("未找到预期的OCR数据键，使用完整结果")
                    ocr_data = result

                processed_result = process_predict_result(result)

            elif isinstance(result, list) and len(result) > 0:
                # 如果result是列表格式（旧版本格式）
                logger.info("结果是列表格式")
                if result[0]:
                    logger.info(f"result[0]类型: {type(result[0])}")
                    logger.info(f"result[0]长度: {len(result[0]) if result[0] else 0}")
                    logger.info(f"result[0]内容: {result[0]}")

                    processed_result = process_ocr_result(result[0])
                else:
                    processed_result = []
            else:
                logger.warning(f"未知的结果格式: {type(result)}")
                processed_result = []

            logger.info(f"OCR识别成功，识别到 {len(processed_result)} 个文本区域")

            return jsonify({
                "success": True,
                "message": "OCR识别成功",
                "data": processed_result
            })
        else:
            logger.info("OCR识别完成，但未识别到文字")
            return jsonify({
                "success": True,
                "message": "未识别到文字内容",
                "data": []
            })

    except Exception as e:
        logger.error(f"OCR识别过程中发生错误: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"OCR识别失败: {str(e)}",
            "data": None
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        "success": False,
        "message": "接口不存在",
        "data": None
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        "success": False,
        "message": "服务器内部错误",
        "data": None
    }), 500

if __name__ == '__main__':
    try:
        # 初始化OCR
        init_ocr()

        # 启动Flask应用
        logger.info("启动PaddleOCR API服务...")
        app.run(
            host='0.0.0.0',
            port=8000,
            debug=False,
            threaded=True
        )
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        exit(1)
