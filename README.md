# PaddleOCR API 服务

这是一个基于PaddleOCR的文字识别API服务，提供简单易用的OCR接口。

## 项目功能

- 支持图片URL和本地图片文件的OCR识别
- 返回JSON格式的识别结果
- 基于Docker容器化部署
- 使用PaddleOCR 3.0.0版本

## API接口说明

### POST /ocr
对图片进行OCR文字识别

**请求参数：**
- `input`: 图片输入，支持以下格式：
  - 图片URL（如：https://example.com/image.jpg）
  - 本地图片文件路径
  - Base64编码的图片数据

**请求示例：**
```json
{
    "input": "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"
}
```

**返回格式：**
```json
{
    "success": true,
    "data": [
        {
            "text": "识别的文字内容",
            "confidence": 0.95,
            "bbox": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
        }
    ],
    "message": "OCR识别成功"
}
```

### GET /health
健康检查接口

**返回格式：**
```json
{
    "status": "healthy",
    "service": "PaddleOCR API"
}
```

## 部署说明

### 使用Docker部署

1. 构建Docker镜像：

docker build -t paddleocr-api .
```

2. 运行容器：

docker run -p 8000:8000 paddleocr-api
```

### 测试API

```bash
# 使用测试脚本
python test_api.py

# 或者直接使用curl
curl -X POST http://localhost:8000/ocr \
  -H "Content-Type: application/json" \
  -d '{"input": "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"}'
```

## 技术架构

- **Web框架**: Flask
- **OCR引擎**: PaddleOCR 3.0.0
- **容器化**: Docker
- **Python版本**: 3.8+

## 注意事项

- 首次运行时会自动下载PaddleOCR模型文件，可能需要较长时间
- 建议在有GPU的环境中运行以获得更好的性能
- 支持的图片格式：JPG, PNG, BMP等常见格式

## 更新日志

- v1.1.0: 修复图片输入格式问题
  - 修复了PaddleOCR不支持BytesIO对象的问题
  - 将URL和Base64图片输入转换为numpy数组格式
  - 添加了opencv-python依赖来处理图片解码
  - 添加了paddlepaddle核心依赖
- v1.0.0: 初始版本，支持基本OCR功能

## 问题解决记录

### 问题1: 依赖包冲突
**现象**: Docker构建时出现numpy版本冲突
**解决**: 使用宽松的版本要求，让pip自动解决依赖冲突

### 问题2: 缺少PaddlePaddle依赖
**现象**: 运行时报错"No module named 'paddle'"
**解决**: 在requirements.txt中添加paddlepaddle>=2.4.0

### 问题3: 图片输入格式不支持
**现象**: PaddleOCR报错"Not supported input data type! Only numpy.ndarray and str are supported!"
**解决**: 将BytesIO对象转换为numpy数组格式，使用cv2.imdecode进行图片解码
