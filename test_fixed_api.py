#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的PaddleOCR API
"""

import urllib.request
import urllib.parse
import json
import time

def test_health():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        with urllib.request.urlopen('http://localhost:8000/health') as response:
            result = json.loads(response.read().decode('utf-8'))
            print(f"✅ 健康检查成功: {result}")
            return True
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_ocr():
    """测试OCR接口"""
    print("\n🔍 测试OCR接口...")
    
    url = "http://localhost:8000/ocr"
    data = {
        "input": "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"
    }
    
    json_data = json.dumps(data).encode('utf-8')
    req = urllib.request.Request(
        url,
        data=json_data,
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        print("📤 发送OCR请求...")
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
            
            print(f"📊 响应状态: {response.status}")
            print(f"✅ 请求成功: {result.get('success', False)}")
            print(f"📝 消息: {result.get('message', '')}")
            
            data_list = result.get('data', [])
            print(f"🔢 识别到文本数量: {len(data_list)}")
            
            if data_list:
                print("\n📋 识别结果:")
                for i, item in enumerate(data_list[:10]):  # 只显示前10个结果
                    text = item.get('text', '')
                    confidence = item.get('confidence', 0)
                    bbox = item.get('bbox', [])
                    
                    print(f"  {i+1:2d}. 文本: '{text}' | 置信度: {confidence:.4f} | 坐标点数: {len(bbox)}")
                
                if len(data_list) > 10:
                    print(f"  ... 还有 {len(data_list) - 10} 个结果")
                
                # 验证结果格式
                print("\n🔍 格式验证:")
                first_item = data_list[0]
                
                # 检查文本
                if isinstance(first_item.get('text'), str) and first_item.get('text').strip():
                    print("  ✅ 文本格式正确")
                else:
                    print("  ❌ 文本格式错误")
                
                # 检查置信度
                confidence = first_item.get('confidence')
                if isinstance(confidence, (int, float)) and 0 <= confidence <= 1:
                    print("  ✅ 置信度格式正确")
                else:
                    print("  ❌ 置信度格式错误")
                
                # 检查bbox
                bbox = first_item.get('bbox')
                if isinstance(bbox, list) and len(bbox) > 0:
                    print("  ✅ bbox格式正确")
                else:
                    print("  ❌ bbox格式错误")
            
            return result.get('success', False)
            
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的PaddleOCR API...")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(5)
    
    # 测试健康检查
    health_ok = test_health()
    if not health_ok:
        print("\n❌ 健康检查失败，请确保服务正在运行")
        return
    
    # 测试OCR功能
    ocr_ok = test_ocr()
    
    print("\n" + "=" * 50)
    if ocr_ok:
        print("🎉 所有测试通过！OCR API修复成功！")
    else:
        print("❌ OCR测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
